// Rectangle Drawing Tool for Tradelab Chart
// Inspired by TradingView lightweight-charts plugin example

const RectangleDrawingTool = (() => {
	let isActive = false;
	let startPoint = null;
	let endPoint = null;
	let currentRect = null;
	let color = '#2962FF'; // Default color
	let chartContainer = null;
	let isDrawing = false; // Track if we're currently drawing
	let rectangles = []; // Store all drawn rectangles

	// Activate drawing mode
	function activate(container) {
		if (isActive) return;
		console.log('Activating rectangle tool on container:', container);
		isActive = true;
		chartContainer = container;
		container.style.cursor = 'crosshair';
		container.addEventListener('mousedown', onMouseDown);
		container.addEventListener('mousemove', onMouseMove);
		container.addEventListener('mouseup', onMouseUp);
		console.log('Rectangle tool activated successfully');
	}

	// Deactivate drawing mode
	function deactivate() {
		if (!isActive || !chartContainer) return;
		isActive = false;
		isDrawing = false;
		chartContainer.style.cursor = '';
		chartContainer.removeEventListener('mousedown', onMouseDown);
		chartContainer.removeEventListener('mousemove', onMouseMove);
		chartContainer.removeEventListener('mouseup', onMouseUp);

		// Clean up all rectangles
		rectangles.forEach(rect => {
			if (rect.parentNode) {
				rect.parentNode.removeChild(rect);
			}
		});
		rectangles = [];

		startPoint = null;
		endPoint = null;
		currentRect = null;
		chartContainer = null;
	}

	// Set rectangle color
	function setColor(newColor) {
		color = newColor;
		if (currentRect) {
			currentRect.style.borderColor = color;
			currentRect.style.backgroundColor = hexToRgba(color, 0.18);
		}
	}

	// Clear all rectangles
	function clearAllRectangles() {
		rectangles.forEach(rect => {
			if (rect.parentNode) {
				rect.parentNode.removeChild(rect);
			}
		});
		rectangles = [];

		// Also clear current drawing rectangle if any
		if (currentRect && currentRect.parentNode) {
			currentRect.parentNode.removeChild(currentRect);
			currentRect = null;
		}

		// Reset drawing state
		isDrawing = false;
		startPoint = null;
		endPoint = null;

		console.log('All rectangles cleared');
	}

	// Mouse event handlers
	function onMouseDown(e) {
		if (!isActive) return;
		console.log('Mouse down at:', e.offsetX, e.offsetY);

		// Only start a new rectangle if we're not already drawing
		if (!isDrawing) {
			isDrawing = true;
			startPoint = { x: e.offsetX, y: e.offsetY };

			// Create new rectangle for drawing
			currentRect = document.createElement('div');
			Object.assign(currentRect.style, {
				position: 'absolute',
				border: `2px solid ${color}`,
				background: hexToRgba(color, 0.18),
				pointerEvents: 'none',
				zIndex: 1000,
			});
			chartContainer.appendChild(currentRect);
			console.log('Rectangle element created and added to container');
		}
	}

	function onMouseMove(e) {
		if (!isActive || !isDrawing || !startPoint || !currentRect) return;
		endPoint = { x: e.offsetX, y: e.offsetY };
		updateRect();
	}

	function onMouseUp(e) {
		if (!isActive || !isDrawing || !startPoint || !currentRect) return;
		endPoint = { x: e.offsetX, y: e.offsetY };
		updateRect();

		// Finalize the rectangle - add it to the rectangles array
		rectangles.push(currentRect);

		// Reset drawing state for next rectangle
		isDrawing = false;
		startPoint = null;
		endPoint = null;
		currentRect = null;

		console.log('Rectangle drawing completed. Total rectangles:', rectangles.length);
		// Tool remains active for drawing more rectangles
	}

	function updateRect() {
		if (!startPoint || !endPoint || !currentRect) return;
		const x = Math.min(startPoint.x, endPoint.x);
		const y = Math.min(startPoint.y, endPoint.y);
		const w = Math.abs(startPoint.x - endPoint.x);
		const h = Math.abs(startPoint.y - endPoint.y);
		console.log('Updating rectangle:', { x, y, w, h });
		Object.assign(currentRect.style, {
			left: `${x}px`,
			top: `${y}px`,
			width: `${w}px`,
			height: `${h}px`,
		});
	}

	// Utility: convert hex to rgba
	function hexToRgba(hex, alpha) {
		let c = hex.replace('#', '');
		if (c.length === 3) c = c.split('').map(x => x + x).join('');
		const num = parseInt(c, 16);
		const r = (num >> 16) & 255;
		const g = (num >> 8) & 255;
		const b = num & 255;
		return `rgba(${r},${g},${b},${alpha})`;
	}

	// Expose API
	return {
		activate,
		deactivate,
		setColor,
		clearAllRectangles,
		isActive: () => isActive,
	};
})();

// Export for use in chart.js and top_navbar.js
window.RectangleDrawingTool = RectangleDrawingTool;
