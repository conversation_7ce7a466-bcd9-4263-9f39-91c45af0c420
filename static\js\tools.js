// Rectangle Drawing Tool for Tradelab Chart
// Inspired by TradingView lightweight-charts plugin example

const RectangleDrawingTool = (() => {
	let isActive = false;
	let startPoint = null;
	let endPoint = null;
	let currentRect = null;
	let color = '#2962FF'; // Default color
	let chartContainer = null;
	let isDrawing = false; // Track if we're currently drawing
	let rectangles = []; // Store all drawn rectangles with metadata
	let chart = null; // Reference to the chart instance
	let selectedRect = null; // Currently selected rectangle for editing

	// Activate drawing mode
	function activate(container, chartInstance) {
		if (isActive) return;
		console.log('Activating rectangle tool on container:', container);
		isActive = true;
		chartContainer = container;
		chart = chartInstance;
		container.style.cursor = 'crosshair';
		container.addEventListener('mousedown', onMouseDown);
		container.addEventListener('mousemove', onMouseMove);
		console.log('Rectangle tool activated successfully - Click to start, move mouse, click again to finish');
	}

	// Temporarily activate for editing (doesn't change button state)
	function activateForEditing(container, chartInstance) {
		if (isActive) return;
		chartContainer = container;
		chart = chartInstance;
		container.addEventListener('mousedown', onMouseDown);
	}

	// Auto-deactivate after drawing (keeps rectangles)
	function autoDeactivate() {
		if (!isActive || !chartContainer) return;
		isActive = false;
		isDrawing = false;
		chartContainer.style.cursor = '';
		chartContainer.removeEventListener('mousedown', onMouseDown);
		chartContainer.removeEventListener('mousemove', onMouseMove);

		// Reset drawing state but keep rectangles
		startPoint = null;
		endPoint = null;
		currentRect = null;

		console.log('Tool auto-deactivated, rectangles preserved');
	}

	// Deactivate drawing mode and clear all rectangles
	function deactivate() {
		if (!isActive || !chartContainer) return;
		isActive = false;
		isDrawing = false;
		chartContainer.style.cursor = '';
		chartContainer.removeEventListener('mousedown', onMouseDown);
		chartContainer.removeEventListener('mousemove', onMouseMove);

		// Clean up all rectangles
		rectangles.forEach(rectData => {
			if (rectData.element && rectData.element.parentNode) {
				rectData.element.parentNode.removeChild(rectData.element);
			}
		});
		rectangles = [];

		startPoint = null;
		endPoint = null;
		currentRect = null;
		selectedRect = null;
		chart = null;
		chartContainer = null;
	}

	// Set rectangle color
	function setColor(newColor) {
		color = newColor;
		if (currentRect) {
			currentRect.style.borderColor = color;
			currentRect.style.backgroundColor = hexToRgba(color, 0.18);
		}
	}

	// Show rectangle editor popup
	function showRectangleEditor(rectData) {
		selectedRect = rectData;

		// Remove any existing editor
		const existingEditor = document.getElementById('rectangle-editor');
		if (existingEditor) existingEditor.remove();

		// Create editor popup
		const editor = document.createElement('div');
		editor.id = 'rectangle-editor';
		editor.style.cssText = `
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			background: #2a2d3a;
			border: 1px solid #444;
			border-radius: 8px;
			padding: 20px;
			z-index: 10000;
			color: white;
			font-family: Arial, sans-serif;
			box-shadow: 0 4px 20px rgba(0,0,0,0.5);
			min-width: 300px;
		`;

		editor.innerHTML = `
			<h3 style="margin: 0 0 15px 0; color: #fff;">Edit Rectangle</h3>
			<div style="margin-bottom: 10px;">
				<label style="display: block; margin-bottom: 5px;">Color:</label>
				<input type="color" id="rect-color" value="${rectData.color}" style="width: 100%; height: 30px;">
			</div>
			<div style="margin-bottom: 10px;">
				<label style="display: block; margin-bottom: 5px;">Position X:</label>
				<input type="number" id="rect-x" value="${parseInt(rectData.element.style.left)}" style="width: 100%; padding: 5px;">
			</div>
			<div style="margin-bottom: 10px;">
				<label style="display: block; margin-bottom: 5px;">Position Y:</label>
				<input type="number" id="rect-y" value="${parseInt(rectData.element.style.top)}" style="width: 100%; padding: 5px;">
			</div>
			<div style="margin-bottom: 10px;">
				<label style="display: block; margin-bottom: 5px;">Width:</label>
				<input type="number" id="rect-width" value="${parseInt(rectData.element.style.width)}" style="width: 100%; padding: 5px;">
			</div>
			<div style="margin-bottom: 15px;">
				<label style="display: block; margin-bottom: 5px;">Height:</label>
				<input type="number" id="rect-height" value="${parseInt(rectData.element.style.height)}" style="width: 100%; padding: 5px;">
			</div>
			<div style="display: flex; gap: 10px;">
				<button id="apply-changes" style="flex: 1; padding: 8px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Apply</button>
				<button id="delete-rect" style="flex: 1; padding: 8px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete</button>
				<button id="cancel-edit" style="flex: 1; padding: 8px; background: #666; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
			</div>
		`;

		document.body.appendChild(editor);

		// Add event listeners
		document.getElementById('apply-changes').onclick = () => applyRectangleChanges(rectData);
		document.getElementById('delete-rect').onclick = () => deleteRectangle(rectData);
		document.getElementById('cancel-edit').onclick = () => editor.remove();

		// Close on Escape key
		const escapeHandler = (e) => {
			if (e.key === 'Escape') {
				editor.remove();
				document.removeEventListener('keydown', escapeHandler);
			}
		};
		document.addEventListener('keydown', escapeHandler);
	}

	// Apply changes to rectangle
	function applyRectangleChanges(rectData) {
		const colorInput = document.getElementById('rect-color');
		const xInput = document.getElementById('rect-x');
		const yInput = document.getElementById('rect-y');
		const widthInput = document.getElementById('rect-width');
		const heightInput = document.getElementById('rect-height');

		// Update rectangle properties
		rectData.color = colorInput.value;

		// Update pixel-based positioning (for manual adjustments)
		Object.assign(rectData.element.style, {
			borderColor: colorInput.value,
			backgroundColor: hexToRgba(colorInput.value, 0.18),
			left: `${xInput.value}px`,
			top: `${yInput.value}px`,
			width: `${widthInput.value}px`,
			height: `${heightInput.value}px`
		});

		// Update logical coordinates based on new pixel position
		if (chart && chart.timeScale && chart.priceScale) {
			try {
				const timeScale = chart.timeScale();
				const priceScale = chart.priceScale();

				const x1 = parseInt(xInput.value);
				const y1 = parseInt(yInput.value);
				const x2 = x1 + parseInt(widthInput.value);
				const y2 = y1 + parseInt(heightInput.value);

				rectData.logicalStart = {
					time: timeScale.coordinateToTime(x1),
					price: priceScale.coordinateToPrice(y1)
				};
				rectData.logicalEnd = {
					time: timeScale.coordinateToTime(x2),
					price: priceScale.coordinateToPrice(y2)
				};
			} catch (error) {
				console.warn('Could not update logical coordinates:', error);
			}
		}

		// Close editor
		document.getElementById('rectangle-editor').remove();
		console.log('Rectangle updated:', rectData.id);
	}

	// Delete individual rectangle
	function deleteRectangle(rectData) {
		// Remove from DOM
		if (rectData.element && rectData.element.parentNode) {
			rectData.element.parentNode.removeChild(rectData.element);
		}

		// Remove from array
		const index = rectangles.findIndex(r => r.id === rectData.id);
		if (index > -1) {
			rectangles.splice(index, 1);
		}

		// Close editor
		document.getElementById('rectangle-editor').remove();
		console.log('Rectangle deleted:', rectData.id);
	}

	// Clear all rectangles
	function clearAllRectangles() {
		rectangles.forEach(rectData => {
			if (rectData.element && rectData.element.parentNode) {
				rectData.element.parentNode.removeChild(rectData.element);
			}
		});
		rectangles = [];

		// Also clear current drawing rectangle if any
		if (currentRect && currentRect.parentNode) {
			currentRect.parentNode.removeChild(currentRect);
			currentRect = null;
		}

		// Reset drawing state
		isDrawing = false;
		startPoint = null;
		endPoint = null;
		selectedRect = null;

		console.log('All rectangles cleared');
	}

	// Helper function to get crosshair coordinates from chart
	function getCrosshairCoordinates(e) {
		const pixelCoords = { x: e.offsetX, y: e.offsetY };

		if (chart && chart.timeScale && chart.priceScale) {
			try {
				// Get the chart's coordinate system
				const timeScale = chart.timeScale();
				const priceScale = chart.priceScale();

				// Convert pixel coordinates to logical coordinates
				const time = timeScale.coordinateToTime(e.offsetX);
				const price = priceScale.coordinateToPrice(e.offsetY);

				// Validate the coordinates
				if (time !== null && price !== null && !isNaN(price)) {
					return {
						pixel: pixelCoords,
						logical: { time, price },
						// Store coordinate conversion functions for later use
						timeScale: timeScale,
						priceScale: priceScale
					};
				}
			} catch (error) {
				console.warn('Could not get crosshair coordinates:', error);
			}
		}

		// Fallback to pixel coordinates
		return {
			pixel: pixelCoords,
			logical: null,
			timeScale: null,
			priceScale: null
		};
	}

	// Convert logical coordinates back to pixel coordinates
	function logicalToPixel(logicalCoords, timeScale, priceScale) {
		if (!logicalCoords || !timeScale || !priceScale) {
			return null;
		}

		try {
			const x = timeScale.timeToCoordinate(logicalCoords.time);
			const y = priceScale.priceToCoordinate(logicalCoords.price);

			if (x !== null && y !== null) {
				return { x, y };
			}
		} catch (error) {
			console.warn('Could not convert logical to pixel coordinates:', error);
		}

		return null;
	}

	// Check if click is on an existing rectangle
	function getClickedRectangle(x, y) {
		for (let i = rectangles.length - 1; i >= 0; i--) {
			const rectData = rectangles[i];
			const rect = rectData.element.getBoundingClientRect();
			const containerRect = chartContainer.getBoundingClientRect();

			const relativeX = x + containerRect.left;
			const relativeY = y + containerRect.top;

			if (relativeX >= rect.left && relativeX <= rect.right &&
				relativeY >= rect.top && relativeY <= rect.bottom) {
				return rectData;
			}
		}
		return null;
	}

	// Mouse event handlers
	function onMouseDown(e) {
		if (!isActive) return;

		const coords = getCrosshairCoordinates(e);
		console.log('Mouse click at:', coords);

		// Check for Alt+click on existing rectangle
		if (e.altKey) {
			const clickedRect = getClickedRectangle(e.offsetX, e.offsetY);
			if (clickedRect) {
				showRectangleEditor(clickedRect);
				return;
			}
		}

		if (!isDrawing) {
			// First click - start drawing using crosshair coordinates
			if (coords.logical) {
				isDrawing = true;
				startPoint = coords;

				// Create new rectangle for drawing
				currentRect = document.createElement('div');
				Object.assign(currentRect.style, {
					position: 'absolute',
					border: `2px solid ${color}`,
					background: hexToRgba(color, 0.18),
					pointerEvents: 'none',
					zIndex: 1000,
				});
				chartContainer.appendChild(currentRect);
				console.log('Rectangle started at crosshair:', coords.logical);
			} else {
				console.warn('Could not get crosshair coordinates for rectangle start');
			}
		} else {
			// Second click - finish drawing using crosshair coordinates
			if (coords.logical) {
				endPoint = coords;
				updateRectangleFromLogical();

				// Create rectangle data object with logical coordinates
				const rectData = {
					id: Date.now() + Math.random(), // Unique ID
					element: currentRect,
					startPoint: startPoint,
					endPoint: endPoint,
					color: color,
					created: new Date(),
					// Store logical coordinates for proper repositioning on chart updates
					logicalStart: startPoint.logical,
					logicalEnd: endPoint.logical
				};

				// Finalize the rectangle
				rectangles.push(rectData);

				// Reset drawing state and auto-deactivate
				isDrawing = false;
				startPoint = null;
				endPoint = null;
				currentRect = null;

				console.log('Rectangle completed at crosshair:', coords.logical);

				// Auto-deactivate the tool (keeps rectangles)
				autoDeactivate();

				// Update navbar button state
				const rectBtn = document.querySelector('#rectangle-tool-btn');
				if (rectBtn) {
					rectBtn.classList.remove('active');
				}
			} else {
				console.warn('Could not get crosshair coordinates for rectangle end');
			}
		}
	}

	function onMouseMove(e) {
		if (!isActive || !isDrawing || !startPoint || !currentRect) return;
		// Update rectangle preview as mouse moves using crosshair coordinates
		const coords = getCrosshairCoordinates(e);
		if (coords.logical) {
			endPoint = coords;
			updateRectangleFromLogical();
		}
	}

	// Note: onMouseUp is not needed for click-move-click behavior

	// Update rectangle using logical coordinates (crosshair-based)
	function updateRectangleFromLogical() {
		if (!startPoint || !endPoint || !currentRect) return;

		// Convert logical coordinates to current pixel coordinates
		const timeScale = chart.timeScale();
		const priceScale = chart.priceScale();

		const startPixel = logicalToPixel(startPoint.logical, timeScale, priceScale);
		const endPixel = logicalToPixel(endPoint.logical, timeScale, priceScale);

		if (!startPixel || !endPixel) {
			console.warn('Could not convert logical coordinates to pixels');
			return;
		}

		const x = Math.min(startPixel.x, endPixel.x);
		const y = Math.min(startPixel.y, endPixel.y);
		const w = Math.abs(startPixel.x - endPixel.x);
		const h = Math.abs(startPixel.y - endPixel.y);

		console.log('Updating rectangle from logical coords:', {
			start: startPoint.logical,
			end: endPoint.logical,
			pixel: { x, y, w, h }
		});

		Object.assign(currentRect.style, {
			left: `${x}px`,
			top: `${y}px`,
			width: `${w}px`,
			height: `${h}px`,
		});
	}

	// Update existing rectangle from stored logical coordinates
	function updateExistingRectangle(rectData) {
		if (!rectData.logicalStart || !rectData.logicalEnd || !rectData.element) return;

		const timeScale = chart.timeScale();
		const priceScale = chart.priceScale();

		const startPixel = logicalToPixel(rectData.logicalStart, timeScale, priceScale);
		const endPixel = logicalToPixel(rectData.logicalEnd, timeScale, priceScale);

		if (!startPixel || !endPixel) return;

		const x = Math.min(startPixel.x, endPixel.x);
		const y = Math.min(startPixel.y, endPixel.y);
		const w = Math.abs(startPixel.x - endPixel.x);
		const h = Math.abs(startPixel.y - endPixel.y);

		Object.assign(rectData.element.style, {
			left: `${x}px`,
			top: `${y}px`,
			width: `${w}px`,
			height: `${h}px`,
		});
	}

	// Utility: convert hex to rgba
	function hexToRgba(hex, alpha) {
		let c = hex.replace('#', '');
		if (c.length === 3) c = c.split('').map(x => x + x).join('');
		const num = parseInt(c, 16);
		const r = (num >> 16) & 255;
		const g = (num >> 8) & 255;
		const b = num & 255;
		return `rgba(${r},${g},${b},${alpha})`;
	}

	// Handle Alt+click from global event listener
	function handleAltClick(e, container, chartInstance) {
		// Temporarily set up the context
		const wasActive = isActive;
		chartContainer = container;
		chart = chartInstance;

		// Check for clicked rectangle
		const clickedRect = getClickedRectangle(e.offsetX, e.offsetY);
		if (clickedRect) {
			showRectangleEditor(clickedRect);
		}

		// Restore previous state if tool wasn't active
		if (!wasActive) {
			chartContainer = null;
			chart = null;
		}
	}

	// Update all rectangles when chart changes (zoom, scroll, resize)
	function updateAllRectangles() {
		if (!chart) return;

		rectangles.forEach(rectData => {
			updateExistingRectangle(rectData);
		});
	}

	// Expose API
	return {
		activate,
		deactivate,
		setColor,
		clearAllRectangles,
		handleAltClick,
		updateAllRectangles,
		isActive: () => isActive,
	};
})();

// Export for use in chart.js and top_navbar.js
window.RectangleDrawingTool = RectangleDrawingTool;
