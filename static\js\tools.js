// Rectangle Drawing Tool for Tradelab Chart
// Inspired by TradingView lightweight-charts plugin example

const RectangleDrawingTool = (() => {
	let isActive = false;
	let startPoint = null;
	let endPoint = null;
	let currentRect = null;
	let color = '#2962FF'; // Default color
	let chartContainer = null;

	// Activate drawing mode
	function activate(container) {
		if (isActive) return;
		console.log('Activating rectangle tool on container:', container);
		isActive = true;
		chartContainer = container;
		container.style.cursor = 'crosshair';
		container.addEventListener('mousedown', onMouseDown);
		container.addEventListener('mousemove', onMouseMove);
		container.addEventListener('mouseup', onMouseUp);
		console.log('Rectangle tool activated successfully');
	}

	// Deactivate drawing mode
	function deactivate() {
		if (!isActive || !chartContainer) return;
		isActive = false;
		chartContainer.style.cursor = '';
		chartContainer.removeEventListener('mousedown', onMouseDown);
		chartContainer.removeEventListener('mousemove', onMouseMove);
		chartContainer.removeEventListener('mouseup', onMouseUp);
		startPoint = null;
		endPoint = null;
		currentRect = null;
		chartContainer = null;
	}

	// Set rectangle color
	function setColor(newColor) {
		color = newColor;
		if (currentRect) {
			currentRect.style.borderColor = color;
			currentRect.style.backgroundColor = hexToRgba(color, 0.18);
		}
	}

	// Mouse event handlers
	function onMouseDown(e) {
		if (!isActive) return;
		console.log('Mouse down at:', e.offsetX, e.offsetY);
		startPoint = { x: e.offsetX, y: e.offsetY };
		if (currentRect) {
			chartContainer.removeChild(currentRect);
			currentRect = null;
		}
		currentRect = document.createElement('div');
		Object.assign(currentRect.style, {
			position: 'absolute',
			border: `2px solid ${color}`,
			background: hexToRgba(color, 0.18),
			pointerEvents: 'none',
			zIndex: 1000,
		});
		chartContainer.appendChild(currentRect);
		console.log('Rectangle element created and added to container');
	}

	function onMouseMove(e) {
		if (!isActive || !startPoint || !currentRect) return;
		endPoint = { x: e.offsetX, y: e.offsetY };
		updateRect();
	}

	function onMouseUp(e) {
		if (!isActive || !startPoint || !currentRect) return;
		endPoint = { x: e.offsetX, y: e.offsetY };
		updateRect();
		// Optionally, keep the rectangle or deactivate tool
		// deactivate(); // Uncomment to exit drawing mode after one rectangle
	}

	function updateRect() {
		if (!startPoint || !endPoint || !currentRect) return;
		const x = Math.min(startPoint.x, endPoint.x);
		const y = Math.min(startPoint.y, endPoint.y);
		const w = Math.abs(startPoint.x - endPoint.x);
		const h = Math.abs(startPoint.y - endPoint.y);
		console.log('Updating rectangle:', { x, y, w, h });
		Object.assign(currentRect.style, {
			left: `${x}px`,
			top: `${y}px`,
			width: `${w}px`,
			height: `${h}px`,
		});
	}

	// Utility: convert hex to rgba
	function hexToRgba(hex, alpha) {
		let c = hex.replace('#', '');
		if (c.length === 3) c = c.split('').map(x => x + x).join('');
		const num = parseInt(c, 16);
		const r = (num >> 16) & 255;
		const g = (num >> 8) & 255;
		const b = num & 255;
		return `rgba(${r},${g},${b},${alpha})`;
	}

	// Expose API
	return {
		activate,
		deactivate,
		setColor,
		isActive: () => isActive,
	};
})();

// Export for use in chart.js and top_navbar.js
window.RectangleDrawingTool = RectangleDrawingTool;
